:root {
  --color-white: #ffffff;
}

@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
}

html,
body {
  height: 100%;
}

body {
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  font: inherit;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

#root,
#__next {
  isolation: isolate;
}

/*Custom Pagination*/
.swiper-pagination {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 12px !important;
  margin-top: 32px !important;
  padding-bottom: 8px !important;
  position: relative !important;
  bottom: 0 !important;
}

.swiper-pagination-bullet {
  height: 10px !important;
  width: 10px !important;
  background: #BCD8E9 !important;
  border-radius: 50% !important;
  cursor: pointer !important;
  opacity: 1 !important;
  transition: all 0.3s ease !important;
  margin: 0 !important;
}

.swiper-pagination-bullet:hover {
  background: #9BC5E0 !important;
  transform: scale(1.1) !important;
}

.swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: #2AA7FF !important;
  outline: 2px solid #2AA7FF !important;
  outline-offset: 4px !important;
  transform: scale(1.2) !important;
}

/* Ensure consistent spacing for all carousels */
.swiper {
  padding-bottom: 60px !important;
}

/* Custom styles for different carousel types */
.offers-carousel .swiper-pagination {
  margin-top: 24px !important;
}

.specialists-carousel .swiper-pagination {
  margin-top: 28px !important;
}
